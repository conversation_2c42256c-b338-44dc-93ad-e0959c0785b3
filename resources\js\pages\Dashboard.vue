<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';

// Import icons
import {
  ClipboardDocumentCheckIcon,
  UserIcon,
  UsersIcon,
  UserGroupIcon,
  DocumentCheckIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Overview',
        href: '/dashboard',
    },
];

// Sample data
const services = [
    {
        date: '2025-01-20',
        transactionId: 'TXN_123456',
        concern: 'Concern 1',
        serviceType: 'Individual Counseling',
        status: 'Pending'
    },
    {
        date: '2025-01-15',
        transactionId: 'TXN_123457',
        concern: 'Concern 2',
        serviceType: 'Conference/Consultation',
        status: 'Pending'
    },
    {
        date: '2025-01-05',
        transactionId: 'TXN_123458',
        concern: 'Concern 3',
        serviceType: 'Parent Conference',
        status: 'Cleared'
    },
    {
        date: '2025-01-02',
        transactionId: 'TXN_123459',
        concern: 'Concern 4',
        serviceType: 'Intake Interview',
        status: 'Cleared'
    }
];

const stats = [
    { title: 'SCRI', value: 'Done', icon: ClipboardDocumentCheckIcon },
    { title: 'Individual Counseling', value: '2', icon: UserIcon },
    { title: 'Group Counseling', value: '1', icon: UsersIcon },
    { title: 'Parent Conference', value: '1', icon: UserGroupIcon },
    { title: 'Assessment', value: '2', icon: DocumentCheckIcon },
    { title: 'Conference/Consultation', value: '2', icon: ChatBubbleLeftRightIcon },
    { title: 'Intake Interview', value: '1', icon: DocumentTextIcon },
    { title: 'Exit Interview', value: 'Not Taken', icon: ArrowRightOnRectangleIcon }
];
</script>

<template>
    <Head title="Overview" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 p-6 overflow-x-auto bg-gray-50">
            
            <!-- Combined Student Profile and Stats Container -->
            <div class="bg-white rounded-xl shadow-lg shadow-blue-100/50 border-l-4 border-[#522B56] border-t border-r border-b border-gray-200">
                <!-- Student Profile Header -->
                <div class="p-6 border-b border-gray-200">
                    <h1 class="text-2xl font-bold text-gray-800">John Romero</h1>
                </div>

                <!-- Stats Grid -->
                <div class="p-4">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div 
                            v-for="(stat, index) in stats" 
                            :key="index"
                            class="p-4 bg-white rounded-lg border border-gray-200 shadow-sm shadow-blue-400 hover:shadow-blue-200"
                        >
                            <div class="flex items-center gap-3">
                                <component 
                                    :is="stat.icon" 
                                    class="h-6 w-6 text-blue-500"
                                />
                                <h3 class="text-sm font-medium text-gray-500">{{ stat.title }}</h3>
                            </div>
                            <p class="mt-2 text-lg font-semibold text-gray-900 text-center">{{ stat.value }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Counseling Services Table -->
            <div class="bg-white border-l-4 border-[#522B56] border-t border-r border-b border-gray-200 rounded-xl overflow-hidden shadow-lg shadow-blue-100/50">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">Counseling Services</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Transaction ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Nature of Concern</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">Type of Services</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="(service, index) in services" :key="index" class="hover:bg-blue-50/50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">{{ service.date }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-r border-gray-200">{{ service.transactionId }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">{{ service.concern }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">{{ service.serviceType }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="{
                                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full': true,
                                        'bg-yellow-100 text-yellow-800': service.status === 'Pending',
                                        'bg-green-100 text-green-800': service.status === 'Cleared'
                                    }">
                                        {{ service.status }}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between text-sm text-gray-500">
        <div class="flex items-center gap-2">
            <button 
                :disabled="currentPage === 1"
                @click="prevPage"
                class="p-1 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>
            <span>Page {{ currentPage }} of {{ totalPages }}</span>
            <button 
                :disabled="currentPage === totalPages"
                @click="nextPage"
                class="p-1 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>
        <span>Total Services: {{ services.length }}</span>
                  </div>
            </div>
        </div>
    </AppLayout>
</template>