<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class ConfirmablePasswordController extends Controller
{
    /**
     * Show the confirm password page.
     */
    public function show(): Response
    {
        return Inertia::render('auth/ConfirmPassword');
    }

    /**
     * Confirm the user's password.
     */
    public function store(Request $request): RedirectResponse
    {
        // Get the current authenticated user
        $user = $request->user();
        
        // Validate using either email or username based on what's available
        $credentials = [
            'password' => $request->password,
        ];

        // Use username if available, otherwise fall back to email
        if (property_exists($user, 'username') && $user->username) {
            $credentials['username'] = $user->username;
        } else {
            $credentials['email'] = $user->email;
        }

        if (! Auth::guard('web')->validate($credentials)) {
            throw ValidationException::withMessages([
                'password' => __('auth.password'),
            ]);
        }

        $request->session()->put('auth.password_confirmed_at', time());

        return redirect()->intended(route('dashboard', absolute: false));
    }
}