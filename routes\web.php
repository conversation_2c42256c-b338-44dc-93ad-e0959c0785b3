<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Redirect root URL to login
Route::redirect('/', '/login')->name('home');

// Dashboard route
Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// SCRI route
Route::get('scri', function () {
    return Inertia::render('SCRI'); 
})->middleware(['auth', 'verified'])->name('scri');

// Assessment route
Route::get('assessment', function () {
    return Inertia::render('Assessment'); 
})->middleware(['auth', 'verified'])->name('assessment');

// Booking route
Route::get('booking', function () {
    return Inertia::render('Booking'); 
})->middleware(['auth', 'verified'])->name('booking');

// Privacy Policy route
Route::get('/privacy-policy', function () {
    return Inertia::render('Privacy-Policy');
})->name('privacy-policy');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';