    <script setup>
    import { ref } from 'vue';
    import { useForm, router } from '@inertiajs/vue3';

    const form = useForm({
        first_name: '',
        last_name: '',
        email: '',
        username: '',
        password: '',
        password_confirmation: '',
        terms: false,
        newsletter: false,
    });

    const showPassword = ref(false);

    const submit = () => {
        form.post('/register', {
            preserveScroll: true,
            onSuccess: () => {
                form.reset();
                router.visit('/login', {
                    method: 'get',
                    replace: true,
                    preserveState: false
                });
            },
            onError: (errors) => {
                if (errors.email) {
                    form.errors.email = "This email is already registered. Please login instead.";
                }
            }
        });
    };

    const togglePassword = () => {
        showPassword.value = !showPassword.value;
    };
    </script>

    <template>
        <div class="container">
            <div class="login-box">
                <div class="title-wrapper">
                    <div class="title-container">
                        <h1 class="title">Register</h1>
                    </div>
                </div>
                <form @submit.prevent="submit" class="form">
                    <div class="grid grid-cols-2 gap-4">
                        <!-- First Name -->
                        <div class="input-group">
                            <input
                                type="text"
                                v-model="form.first_name"
                                placeholder="First Name"
                                required
                                class="input-field"
                                :class="{ 'border-red-500': form.errors.first_name }"
                            />
                            <p v-if="form.errors.first_name" class="error-message">{{ form.errors.first_name }}</p>
                        </div>

                        <!-- Last Name -->
                        <div class="input-group">
                            <input
                                type="text"
                                v-model="form.last_name"
                                placeholder="Last Name"
                                required
                                class="input-field"
                                :class="{ 'border-red-500': form.errors.last_name }"
                            />
                            <p v-if="form.errors.last_name" class="error-message">{{ form.errors.last_name }}</p>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="input-group">
                        <input
                            type="email"
                            v-model="form.email"
                            placeholder="Email"
                            required
                            class="input-field"
                            :class="{ 'border-red-500': form.errors.email }"
                        />
                        <p v-if="form.errors.email" class="error-message">{{ form.errors.email }}</p>
                    </div>

                    <!-- Username -->
                    <div class="input-group">
                        <input
                            type="text"
                            v-model="form.username"
                            placeholder="Username"
                            required
                            class="input-field"
                            :class="{ 'border-red-500': form.errors.username }"
                        />
                        <p v-if="form.errors.username" class="error-message">{{ form.errors.username }}</p>
                    </div>

                    <!-- Password -->
                    <div class="input-group relative">
                        <input
                            :type="showPassword ? 'text' : 'password'"
                            v-model="form.password"
                            placeholder="Password"
                            required
                            class="input-field pr-10"
                            :class="{ 'border-red-500': form.errors.password }"
                        />
                        <button
                            type="button"
                            class="toggle-button"
                            @click="togglePassword"
                            aria-label="Toggle password visibility"
                        >
                            <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            <svg v-else xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                                <line x1="1" y1="1" x2="23" y2="23"></line>
                            </svg>
                        </button>
                        <p v-if="form.errors.password" class="error-message">{{ form.errors.password }}</p>
                    </div>

                    <!-- Confirm Password -->
                    <div class="input-group relative">
                        <input
                            :type="showPassword ? 'text' : 'password'"
                            v-model="form.password_confirmation"
                            placeholder="Confirm Password"
                            required
                            class="input-field pr-10"
                            :class="{ 'border-red-500': form.errors.password_confirmation }"
                        />
                        <p v-if="form.errors.password_confirmation" class="error-message">{{ form.errors.password_confirmation }}</p>
                    </div>

                    <!-- Terms Agreement -->
                    <div class="flex items-start input-group">
                        <div class="flex items-center h-5">
                            <input
                                id="terms"
                                type="checkbox"
                                class="w-4 h-4 border-gray-300 rounded focus:ring-purple-500 text-purple-600"
                                v-model="form.terms"
                                required
                            />
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="terms" class="font-medium text-gray-700">
                                I agree that my information may be stored and used by the Guidance Office.
                            </label>
                        </div>
                    </div>
                    <p v-if="form.errors.terms" class="error-message">{{ form.errors.terms }}</p>

                    <!-- Newsletter Subscription -->
                    <div class="flex items-start input-group">
                        <div class="flex items-center h-5">
                            <input
                                id="newsletter"
                                type="checkbox"
                                class="w-4 h-4 border-gray-300 rounded focus:ring-purple-500 text-purple-600"
                                v-model="form.newsletter"
                            />
                        </div>
                        <div class="ml-3 text-sm">
        <label for="terms" class="font-medium text-gray-700">
            I have read the 
            <a href="/privacy-policy" class="font-semibold underline hover:text-purple-600">privacy and policy</a> 
            and willingly give out details to be registered.
        </label>
    </div>
                    </div>

                    <button 
                        type="submit" 
                        class="login-button" 
                        :disabled="form.processing"
                    >
                        <span v-if="form.processing">Registering...</span>
                        <span v-else>Register</span>
                    </button>

                    <div class="text-center mt-4">
                        <p class="register-text">Already registered? Click here to <a href="/login">Login</a></p>
                    </div>
                </form>
            </div>
        </div>
    </template>

    <style scoped>
    .error-message {
        color: #ef4444;
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }

    .container {
        display: flex;
        min-height: 100vh;
        min-width: 100vw;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        background-image: url('/images/bg.png');
        background-position: center;
        background-size: cover; /* Changed from cover to contain */
    
    }

    .login-box {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        width: 100%;
        max-width: 24rem; /* Reduced from 28em */
        margin-top: 10rem; /* Reduced from 12.5em */
    }

    .title {
        text-align: center;
        font-size: 1.25rem; /* Reduced from 1.5rem */
        margin-top: 0.5em;
        margin-bottom: 0.5em;
        color: white;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        font-weight: 600;
    }

    .title-wrapper {
        position: relative;
        margin: -1.5rem -1.5rem 0 -1.5rem;
        padding: 0.5rem 1.5rem;
        background-color: #522B56;
        border-radius: 1rem 0.5rem 0 0;
    }

    .input-group {
        margin-top: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .input-field {
        width: 100%;
        border-radius: 0.5rem;
        border: 1px solid #ccc;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        outline: none;
        transition: border-color 0.2s;
        color: #000;
    }

    .input-field:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
    }

    .border-red-500 {
        border-color: #ef4444;
    }

    .toggle-button {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.25rem;
        color: #6b7280;
        transition: color 0.2s;
    }

    .toggle-button:hover {
        color: #374151;
    }

    .icon {
        width: 1.25rem;
        height: 1.25rem;
        display: block;
    }

    .login-button {
        width: 100%;
        border-radius: 9999px;
        background-color: #522B56;
        padding: 0.5rem 1rem;
        color: white;
        font-weight: 500;
        transition: background-color 0.2s;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .login-button:hover {
        background-color: #512da8;
    }

    .login-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .register-text {
        color: #2563eb;
        font-size: 0.875rem;
        text-decoration: none;
        transition: color 0.2s;
    }

    .register-text a {
        color: #5e35b1;
    }

    .register-text a:hover {
        text-decoration: underline;
    }
    </style>