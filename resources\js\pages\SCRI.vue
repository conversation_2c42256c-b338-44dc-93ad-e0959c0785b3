<script setup lang="ts">
    import AppLayout from '@/layouts/AppLayout.vue';
    import { type BreadcrumbItem } from '@/types';
     import { Head, router } from '@inertiajs/vue3';
    import { ref, reactive } from 'vue';

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Student Cumulative Records Inventory',
            href: '/scri',
        },
    ];

    const form = reactive({
        surname: '',
        firstName: '',
        middleName: '',
        lastSchoolAttended: '',
        dateOfEntry: '',
        course: '',
        cityAddress: '',
        provincialAddress: '',
        sex: '',
        citizenship: '',
        dateOfBirth: '',
        tribalGroup: '',
        birthplace: '',
        countryOfOrigin: '',
        religion: '',
        alienCertificateNo: '',
        contactNumbers: '',
        email: '',
        fbAccount: '',
        educationalFundSource: '',
        educationalFundOtherSource: '', // Add this for the "Others" input
        fatherName: '',
        fatherStatus: 'living',
        fatherEducation: '',
        fatherOccupation: '',
        fatherOfficeAddress: '',
        fatherContact: '',
        fatherEmail: '',
        fatherOtherIncome: '',
        motherName: '',
        motherStatus: 'living',
        motherEducation: '',
        motherOccupation: '',
        motherOfficeAddress: '',
        motherContact: '',
        motherEmail: '',
        motherOtherIncome: '',
        livingWith: '',
        parentsLivingTogether: '',
        parentsStatus: '',
        parentsStatusWhere: '',
        parentsSeparated: '',
        parentsSeparatedSince: '',
        parentsRemarried: '',
        name: '',
        age: '',
        schoolOccupation: '',
        hasHealthProblems: null,
        healthProblemsSpecify: '',
        doctorRecommendations: '',
        specialNeeds: [],
        signatureFile: null as File | null,
        signaturePreview: null as string | null,
    });

    const handleFundSourceChange = (value: string) => {
        // If clicking the already selected option, deselect it
        if (form.educationalFundSource === value) {
            form.educationalFundSource = '';
            form.educationalFundOtherSource = '';
        } else {
            form.educationalFundSource = value;
            // Clear other source if not selecting "others"
            if (value !== 'others') {
                form.educationalFundOtherSource = '';
            }
        }
    };

    const handleSingleCheckbox = (field: string, value: string) => {
    // If clicking the already selected option, deselect it
    if (form[field] === value) {
        form[field] = '';
        
        // Clear related fields if needed
        if (field === 'parentsStatus') {
            form.parentsStatusWhere = '';
        } else if (field === 'parentsSeparated') {
            form.parentsSeparatedSince = '';
        }
    } else {
        form[field] = value;
    }
};
    
    const fileInput = ref<HTMLInputElement | null>(null);

    const handleFileUpload = (event: Event) => {
        const input = event.target as HTMLInputElement;
        if (input.files && input.files[0]) {
            const file = input.files[0];
            
            // Validate image file
            if (!file.type.match('image.*')) {
                alert('Please select an image file (JPEG, PNG, etc.)');
                return;
            }

            form.signatureFile = file;
            
            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                form.signaturePreview = e.target?.result as string;
            };
            reader.readAsDataURL(file);
        }
    };

    
    const triggerFileUpload = () => {
        if (fileInput.value) {
            fileInput.value.click();
        }
    };

    const goToNextPage = () => {
        router.get('/scri2');
    };

</script>

    <template>
        <Head title="Student Cumulative Records Inventory" />

        <AppLayout :breadcrumbs="breadcrumbs">
            <div class="flex h-full flex-1 flex-col gap-6 p-6 overflow-x-auto bg-gray-50">
                <!-- SCRI Form Container -->
                <div class="bg-white rounded-xl shadow-lg shadow-blue-100/50 border-l-4 border-[#522B56] border-t border-r border-b border-gray-200">
                    <!-- Header -->
                    <div class="p-6 border-b border-gray-200 text-center">
                        <div class="flex justify-center mb-4">
                            <img 
                                src="/images/header.png" 
                                alt="JMC"
                                class="max-w-full h-auto max-h-32 object-contain"
                            >
                        </div>
                        <h2 class="text-lg font-bold text-gray-800 mt-4">Student Cumulative Records Inventory</h2>
                        <h3 class="text-lg font-bold text-gray-800">College Department</h3>
                        <p class="text-xs text-gray-500">(Revised 2022 - 2023)</p>
                    </div>

                    <!-- Form Content -->
                    <div class="p-6 space-y-8">
                        <!-- Basic Information Section -->
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Surname</label>
                                    <input v-model="form.surname" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">First Name</label>
                                    <input v-model="form.firstName" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Middle Name</label>
                                    <input v-model="form.middleName" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Last School Attended</label>
                                    <input v-model="form.lastSchoolAttended" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Date of Entry in JMC</label>
                                        <input v-model="form.dateOfEntry" type="date" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Course</label>
                                        <input v-model="form.course" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Data Section -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">I. Personal Data</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">City Address</label>
                                    <input v-model="form.cityAddress" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Provincial Address</label>
                                    <input v-model="form.provincialAddress" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>

                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Sex</label>
                                    <select v-model="form.sex" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Select</option>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Citizenship</label>
                                    <input v-model="form.citizenship" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
                                    <input v-model="form.dateOfBirth" type="date" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Tribal Group</label>
                                    <input v-model="form.tribalGroup" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Birthplace</label>
                                    <input v-model="form.birthplace" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Country of Origin</label>
                                    <input v-model="form.countryOfOrigin" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Religion</label>
                                    <input v-model="form.religion" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Alien Certificate Registration No.</label>
                                    <input v-model="form.alienCertificateNo" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Contact Numbers (Residence/Mobile)</label>
                                    <input v-model="form.contactNumbers" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">E-mail Address</label>
                                    <input v-model="form.email" type="email" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">FB Account</label>
                                    <input v-model="form.fbAccount" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>
                             </div>

                           <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Source of Educational Fund</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox"
                                            :checked="form.educationalFundSource === 'parent'"
                                            @change="handleFundSourceChange('parent')"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">Parent</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox"
                                            :checked="form.educationalFundSource === 'relatives'"
                                            @change="handleFundSourceChange('relatives')"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">Relatives</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox"
                                            :checked="form.educationalFundSource === 'scholarship'"
                                            @change="handleFundSourceChange('scholarship')"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">Scholarship</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox"
                                            :checked="form.educationalFundSource === 'others'"
                                            @change="handleFundSourceChange('others')"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">Others</label>
                                        <input 
                                            v-if="form.educationalFundSource === 'others'"
                                            v-model="form.educationalFundOtherSource" 
                                            type="text" 
                                            class="ml-2 w-48 border border-gray-300 rounded-md shadow-sm py-1 px-4 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                            placeholder="Specify"
                                        >
                                    </div>
                                </div>
                            </div>
                        
                        <!-- Family Background Section -->
                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">II. A. Family Background</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    <!-- Father's Column -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Father</h4>
                                        <div class="flex items-center space-x-4 mb-4">
                                            <div class="flex items-center">
                                                <input 
                                                    type="checkbox"
                                                    :checked="form.fatherStatus === 'living'"
                                                    @change="handleSingleCheckbox('fatherStatus', 'living')"
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                >
                                                <label class="ml-2 block text-sm text-gray-700">Living</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input 
                                                    type="checkbox"
                                                    :checked="form.fatherStatus === 'deceased'"
                                                    @change="handleSingleCheckbox('fatherStatus', 'deceased')"
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                >
                                                <label class="ml-2 block text-sm text-gray-700">Deceased</label>
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                                <input v-model="form.fatherName" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Educational Attainment</label>
                                                <input v-model="form.fatherEducation" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Occupation</label>
                                                <input v-model="form.fatherOccupation" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Office Address</label>
                                                <input v-model="form.fatherOfficeAddress" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Contact Number</label>
                                                <input v-model="form.fatherContact" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">E-mail Address</label>
                                                <input v-model="form.fatherEmail" type="email" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Other Source of Income</label>
                                                <input v-model="form.fatherOtherIncome" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Mother's Column -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Mother</h4>
                                        <div class="flex items-center space-x-4 mb-4">
                                            <div class="flex items-center">
                                                <input 
                                                    type="checkbox"
                                                    :checked="form.motherStatus === 'living'"
                                                    @change="handleSingleCheckbox('motherStatus', 'living')"
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                >
                                                <label class="ml-2 block text-sm text-gray-700">Living</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input 
                                                    type="checkbox"
                                                    :checked="form.motherStatus === 'deceased'"
                                                    @change="handleSingleCheckbox('motherStatus', 'deceased')"
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                >
                                                <label class="ml-2 block text-sm text-gray-700">Deceased</label>
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                                <input v-model="form.motherName" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Educational Attainment</label>
                                                <input v-model="form.motherEducation" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Occupation</label>
                                                <input v-model="form.motherOccupation" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Office Address</label>
                                                <input v-model="form.motherOfficeAddress" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Contact Number</label>
                                                <input v-model="form.motherContact" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">E-mail Address</label>
                                                <input v-model="form.motherEmail" type="email" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Other Source of Income</label>
                                                <input v-model="form.motherOtherIncome" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    <!-- II.B. Family Background (if not living with parents) -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">II. B. Family Background: (if not living with Parents)</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Relationship to student</label>
                                <input type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Monthly Payment</label>
                                <input type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Contact Number/s</label>
                                <input type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Address</label>
                                <input type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                        </div>
                    </div>

                   <!-- II.C. Family Status -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">II. C. Family Status: Please check your answer.</h3>
                            
                            <div class="space-y-6">
                                <!-- Living With -->
                                <div class="flex items-center">
                                    <label class="block text-sm font-medium text-gray-700 w-65">Are you living with</label>
                                    <div class="flex space-x-4 ml-4">
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.livingWith === 'parents'"
                                                @change="handleSingleCheckbox('livingWith', 'parents')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Parents</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.livingWith === 'grandparents'"
                                                @change="handleSingleCheckbox('livingWith', 'grandparents')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Grandparents</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.livingWith === 'relatives'"
                                                @change="handleSingleCheckbox('livingWith', 'relatives')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Relatives</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.livingWith === 'siblings'"
                                                @change="handleSingleCheckbox('livingWith', 'siblings')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Siblings</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Parents Living Together -->
                                <div class="flex items-center">
                                    <label class="block text-sm font-medium text-gray-700 w-65">Are your parents living together</label>
                                    <div class="flex space-x-4 ml-4">
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsLivingTogether === 'yes'"
                                                @change="handleSingleCheckbox('parentsLivingTogether', 'yes')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Yes</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsLivingTogether === 'no'"
                                                @change="handleSingleCheckbox('parentsLivingTogether', 'no')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">No</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Parents Status -->
                                <div class="flex items-center">
                                    <label class="block text-sm font-medium text-gray-700 w-65">Are your parent/s is/are</label>
                                    <div class="flex space-x-4 ml-4">
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsStatus === 'ofw'"
                                                @change="handleSingleCheckbox('parentsStatus', 'ofw')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">OFW</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsStatus === 'otherTown'"
                                                @change="handleSingleCheckbox('parentsStatus', 'otherTown')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">living in other town/city</label>
                                            <input 
                                                v-if="form.parentsStatus === 'otherTown'"
                                                v-model="form.parentsStatusWhere" 
                                                type="text" 
                                                class="ml-2 w-34 border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                placeholder="Where?"
                                            >
                                        </div>
                                    </div>
                                </div>

                                <!-- Parents Separated -->
                                <div class="flex items-center">
                                    <label class="block text-sm font-medium text-gray-700 w-65">Are your parents separated</label>
                                    <div class="flex space-x-4 ml-4">
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsSeparated === 'yes'"
                                                @change="handleSingleCheckbox('parentsSeparated', 'yes')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Yes</label>
                                            <span v-if="form.parentsSeparated === 'yes'" class="ml-2 text-sm text-gray-700">since when?</span>
                                            <input 
                                                v-if="form.parentsSeparated === 'yes'"
                                                v-model="form.parentsSeparatedSince" 
                                                type="text" 
                                                class="ml-2 w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                placeholder="Year"
                                            >
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsSeparated === 'no'"
                                                @change="handleSingleCheckbox('parentsSeparated', 'no')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">No</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Parents Remarried -->
                                <div class="flex items-center">
                                    <label class="block text-sm font-medium text-gray-700 w-65">Who of your parents remarried</label>
                                    <div class="flex space-x-4 ml-4">
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsRemarried === 'father'"
                                                @change="handleSingleCheckbox('parentsRemarried', 'father')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Father</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox"
                                                :checked="form.parentsRemarried === 'mother'"
                                                @change="handleSingleCheckbox('parentsRemarried', 'mother')"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Mother</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <!-- II.D. Children in the Family -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">II. D. Children in the Family Name</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 80px">Age</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">School/Occupation, Place of Work</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap" style="width: 120px">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap" style="width: 120px">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap" style="width: 120px">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap" style="width: 120px">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap" style="width: 120px">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- II.E. Other Personal Information -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">II. E. Other Personal Information (Please answer the following questions)</h3>
                        
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">1. Are you married?</label>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            :checked="form.married === 'yes'"
                                            @change="handleSingleCheckbox('married', 'yes')"
                                            name="married" 
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">Yes</label>
                                        <input 
                                            v-if="form.married === 'yes'"
                                            v-model="form.marriedToWhom" 
                                            type="text" 
                                            placeholder="To whom?" 
                                            class="ml-2 block w-48 border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        >
                                    </div>
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            :checked="form.married === 'no'"
                                            @change="handleSingleCheckbox('married', 'no')"
                                            name="married" 
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">No</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">2. Do you have children of your own?</label>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            :checked="form.hasChildren === 'yes'"
                                            @change="handleSingleCheckbox('hasChildren', 'yes')"
                                            name="hasChildren" 
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">Yes</label>
                                        <input 
                                            v-if="form.hasChildren === 'yes'"
                                            v-model="form.childrenCount" 
                                            type="text" 
                                            placeholder="How many?" 
                                            class="ml-2 block w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        >
                                    </div>
                                    
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            :checked="form.hasChildren === 'no'"
                                            @change="handleSingleCheckbox('hasChildren', 'no')"
                                            name="hasChildren" 
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label class="ml-2 block text-sm text-gray-700">No</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <label class="block text-sm font-medium text-gray-700 w-70">3. Use 5 adjectives to describe yourself</label>
                                <textarea 
                                    v-model="form.adjectives"
                                    rows="1" 
                                    class="ml-4 block w-full max-w-md border border-gray-300 rounded-md shadow-sm py-1 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                ></textarea>
                            </div>
                            
                            <div class="flex items-center">
                                <label class="block text-sm font-medium text-gray-700 w-40">4. Favorite hobbies</label>
                                <textarea 
                                    v-model="form.hobbies"
                                    rows="1" 
                                    class="ml-4 block w-full max-w-md border border-gray-300 rounded-md shadow-sm py-1 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                ></textarea>
                            </div>
                        </div>
                    </div>
                    <!-- III. Educational Background -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">III. Educational Background:</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of School</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade Completed</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Honors/Award Received</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap font-medium">Personal</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap font-medium">Grade School</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap font-medium">Author High School</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap font-medium">Senior High School</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" class="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                        <!-- IV. Health Data -->
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">IV. Health Data</h3>
                            
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Do you have any health problems?</label>
                                    <div class="flex items-center space-x-4">
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox" 
                                                :checked="form.hasHealthProblems === 'yes'"
                                                @change="handleSingleCheckbox('hasHealthProblems', 'yes')"
                                                name="hasHealthProblems" 
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">Yes</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input 
                                                type="checkbox" 
                                                :checked="form.hasHealthProblems === 'no'"
                                                @change="handleSingleCheckbox('hasHealthProblems', 'no')"
                                                name="hasHealthProblems" 
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            >
                                            <label class="ml-2 block text-sm text-gray-700">No</label>
                                        </div>
                                    </div>
                                
                                    <div v-if="form.hasHealthProblems === 'yes'" class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">If Yes, please specify:</label>
                                        <textarea v-model="form.healthProblemsSpecify" rows="1" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                                        
                                        <label class="block text-sm font-medium text-gray-700 mt-4 mb-1">Please write the Doctor's recommendations:</label>
                                        <textarea v-model="form.doctorRecommendations" rows="1" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Do you have any special needs or attention? Please check that is applicable:</label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="autism" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Autism</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="behavioral problem" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Behavioral problem</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="mental retardation" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Mental retardation</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="learning disability" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Learning disability</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="emotional disturbance" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Emotional disturbance</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="physical disability" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Physical disability</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="involvement in drugs" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Involvement in drugs</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="involvement in gangs" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Involvement in gangs</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="vision impairment" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Vision impairment</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="hearing impairment" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Hearing impairment</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="drug dependency" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Drug dependency</label>
                                        </div>
                                        <div class="flex items-center">
                                            <input v-model="form.specialNeeds" value="other special needs" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label class="ml-2 block text-sm text-gray-700">Other special needs</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="pt-4 mt-4 border-t border-gray-200">
                                    <p class="text-sm italic text-gray-600">I certify that all data answered are true and correct to the best of my knowledge.</p>
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                                    <!-- Left Column - JMCFI-GACC Text -->
                                    <div class="col-span-1 flex items-end">
                                        <p class="text-xs text-gray-500">JMCFI-GACC SCRI FORM 1-8</p>
                                    </div>

                                    <!-- Empty column (spacer) -->
                                    <div class="col-span-2"></div>

                                    <!-- Signature Upload and Next Page Section (Right Side) -->
                                    <div class="col-span-1 flex flex-col">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload Signature</label>
                                        <div class="mt-1 mb-4 flex-grow flex justify-center px-4 pt-5 pb-4 border-2 border-gray-300 border-dashed rounded-md relative">
                                            <!-- Upload interface (shown when no image) -->
                                            <div v-if="!form.signaturePreview" class="space-y-1 text-center w-full">
                                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                                <div class="flex text-sm text-gray-600 justify-center">
                                                    <label for="signature-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                        <span>Upload file</span>
                                                        <input 
                                                            id="signature-upload" 
                                                            name="signature-upload" 
                                                            type="file" 
                                                            accept="image/*"
                                                            @change="handleFileUpload"
                                                            ref="fileInput"
                                                            class="sr-only"
                                                        >
                                                    </label>
                                                </div>
                                                <p class="text-xs text-gray-500">PNG, JPG, GIF</p>
                                            </div>
                                            <!-- Preview (shown when image is uploaded) - Clickable -->
                                            <div v-if="form.signaturePreview" class="flex items-center justify-center w-full h-full">
                                                <label for="signature-upload" class="cursor-pointer">
                                                    <img :src="form.signaturePreview" alt="Signature" class="max-h-20">
                                                </label>
                                            </div>
                                        </div>
                                        <!-- Next Page Button -->
                                        <button 
                                            @click="goToNextPage"
                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#522B56] hover:bg-[#3a1d3d] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#522B56]"
                                        >
                                            Next Page
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 -mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                           </div>
                        </div>
                    </div>
                
            </AppLayout>
        </template>