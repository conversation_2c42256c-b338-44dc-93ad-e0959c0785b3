<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import { LayoutGrid, ClipboardList, FileSearch, Calendar } from 'lucide-vue-next';
import AppLogo from './AppLogo.vue';

const mainNavItems: NavItem[] = [
    {
        title: 'Overview',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'SCRI',
        href: '/scri',
        icon: ClipboardList,
    },
    {
        title: 'Assessment',
        href: '/assessment',
        icon: FileSearch,
    },
    {
        title: 'Booking',
        href: '/booking',
        icon: Calendar,
    },
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset" class="bg-white text-[#522B56]">
        <SidebarHeader class="bg-white">
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('dashboard')">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent class="bg-white">
            <NavMain 
                :items="mainNavItems" 
                class="bg-white"
                :class="{
                    'text-[#522B56]': true,
                    '[&>div>a]:px-4 [&>div>a]:py-3 [&>div>a]:flex [&>div>a]:items-center [&>div>a]:gap-3': true,
                    '[&>div>a.router-link-active]:bg-[#522B56]': true,
                    '[&>div>a.router-link-active]:text-white': true,
                    '[&>div>a.router-link-active]:border-l-[3px]': true,
                    '[&>div>a.router-link-active]:border-[#522B56]': true,
                    '[&>div>a.router-link-active]:font-medium': true,
                }"
            />
        </SidebarContent>

        <SidebarFooter class="bg-white text-[#522B56]">
            <NavFooter 
                :items="footerNavItems" 
                class="bg-white"
                :class="{
                    'text-[#522B56]': true,
                    '[&>div>a]:px-4 [&>div>a]:py-3 [&>div>a]:flex [&>div>a]:items-center [&>div>a]:gap-3': true,
                    '[&>div>a.router-link-active]:bg-[#522B56]': true,
                    '[&>div>a.router-link-active]:text-white': true,
                    '[&>div>a.router-link-active]:border-l-[3px]': true,
                    '[&>div>a.router-link-active]:border-[#522B56]': true,
                    '[&>div>a.router-link-active]:font-medium': true,
                }"
            />
            <NavUser class="bg-white text-[#522B56]" />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>